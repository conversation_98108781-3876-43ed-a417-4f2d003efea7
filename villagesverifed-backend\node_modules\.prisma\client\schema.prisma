generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model User {
  id         Int        @id @default(autoincrement())
  email      String     @unique
  password   String
  fullName   String
  createdAt  DateTime   @default(now())
  businesses Business[]
  reviews    Review[]
  bookings   Booking[]
}

model Business {
  id          Int       @id @default(autoincrement())
  name        String
  description String
  phone       String
  website     String?
  owner       User      @relation(fields: [ownerId], references: [id])
  ownerId     Int
  verified    Boolean   @default(false)
  createdAt   DateTime  @default(now())
  reviews     Review[]
  bookings    Booking[]
}

model Review {
  id         Int      @id @default(autoincrement())
  rating     Int
  comment    String
  user       User     @relation(fields: [userId], references: [id])
  userId     Int
  business   Business @relation(fields: [businessId], references: [id])
  businessId Int
  createdAt  DateTime @default(now())
}

model Booking {
  id         Int      @id @default(autoincrement())
  date       DateTime
  message    String?
  user       User     @relation(fields: [userId], references: [id])
  userId     Int
  business   Business @relation(fields: [businessId], references: [id])
  businessId Int
  status     String   @default("pending")
  createdAt  DateTime @default(now())
}
