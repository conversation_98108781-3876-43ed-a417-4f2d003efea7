<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Find Services | VillagesVerified</title>
    <script src="https://cdn.tailwindcss.com"></script>
  </head>
  <body class="bg-gray-100 text-gray-800">

    <!-- Header -->
    <header class="bg-white shadow-md">
      <div class="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
        <h1 class="text-xl font-bold text-blue-700">VillagesVerified</h1>
        <nav class="space-x-4">
          <a href="index.html" class="text-gray-600 hover:text-blue-600">Home</a>
          <a href="#" class="text-blue-600 font-semibold">Directory</a>
          <a href="#" class="text-gray-600 hover:text-blue-600">Login</a>
        </nav>
      </div>
    </header>

    <!-- Filter/Search Bar -->
    <section class="bg-white py-6 shadow">
      <div class="max-w-7xl mx-auto px-4 grid md:grid-cols-4 gap-4 items-center">
        <input type="text" placeholder="Search for a service or business..."
               class="col-span-2 px-4 py-2 border rounded w-full" />
        <select class="px-4 py-2 border rounded w-full">
          <option>All Categories</option>
          <option>Plumbing</option>
          <option>Landscaping</option>
          <option>Home Cleaning</option>
          <option>Real Estate</option>
        </select>
        <button class="bg-blue-700 text-white py-2 px-4 rounded hover:bg-blue-800">
          Search
        </button>
      </div>
    </section>

    <!-- Listings Section -->
    <main class="max-w-7xl mx-auto px-4 py-10 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      
      <!-- Business Card Example -->
      <div class="bg-white p-5 rounded shadow hover:shadow-lg transition">
        <div class="flex items-center justify-between mb-2">
          <h2 class="text-lg font-bold text-blue-700">Dave's Plumbing Pros</h2>
          <span class="bg-green-100 text-green-700 text-xs px-2 py-1 rounded">Verified</span>
        </div>
        <p class="text-sm text-gray-600 mb-3">Reliable and fast plumbing for homes in The Villages. Over 10 years experience.</p>
        <div class="flex items-center text-yellow-400 text-sm mb-4">
          ★★★★☆ (38 reviews)
        </div>
        <div class="flex justify-between items-center">
          <button class="text-sm text-blue-600 hover:underline">View Profile</button>
          <button class="bg-blue-700 text-white px-4 py-2 rounded text-sm hover:bg-blue-800">Book Now</button>
        </div>
      </div>

      <!-- Duplicate the card above with different data for more listings -->
      <!-- Example listing #2 -->
      <div class="bg-white p-5 rounded shadow hover:shadow-lg transition">
        <div class="flex items-center justify-between mb-2">
          <h2 class="text-lg font-bold text-blue-700">Sunshine Lawn Care</h2>
          <span class="bg-green-100 text-green-700 text-xs px-2 py-1 rounded">Verified</span>
        </div>
        <p class="text-sm text-gray-600 mb-3">Weekly and monthly lawn maintenance. Locally owned and operated.</p>
        <div class="flex items-center text-yellow-400 text-sm mb-4">
          ★★★★★ (52 reviews)
        </div>
        <div class="flex justify-between items-center">
          <button class="text-sm text-blue-600 hover:underline">View Profile</button>
          <button class="bg-blue-700 text-white px-4 py-2 rounded text-sm hover:bg-blue-800">Book Now</button>
        </div>
      </div>

      <!-- Example listing #3 -->
      <div class="bg-white p-5 rounded shadow hover:shadow-lg transition">
        <div class="flex items-center justify-between mb-2">
          <h2 class="text-lg font-bold text-blue-700">The Cleaning Lady Co.</h2>
          <span class="bg-green-100 text-green-700 text-xs px-2 py-1 rounded">Verified</span>
        </div>
        <p class="text-sm text-gray-600 mb-3">Trusted residential cleaning service. Fully insured and flexible scheduling.</p>
        <div class="flex items-center text-yellow-400 text-sm mb-4">
          ★★★★☆ (20 reviews)
        </div>
        <div class="flex justify-between items-center">
          <button class="text-sm text-blue-600 hover:underline">View Profile</button>
          <button class="bg-blue-700 text-white px-4 py-2 rounded text-sm hover:bg-blue-800">Book Now</button>
        </div>
      </div>

    </main>

    <!-- Optional Map Section (for future backend integration) -->
    <section class="bg-white py-8">
      <div class="max-w-7xl mx-auto px-4">
        <h3 class="text-xl font-semibold mb-4">Service Locations</h3>
        <div class="w-full h-64 bg-gray-200 rounded shadow">
          <!-- Google Maps / Leaflet.js placeholder -->
          <p class="text-center pt-24 text-gray-500">Map integration coming soon...</p>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-100 text-gray-600 py-6 text-center mt-12">
      <p>&copy; 2025 VillagesVerified. All rights reserved.</p>
      <div class="mt-2">
        <a href="#" class="text-blue-600 hover:underline">Contact</a> ·
        <a href="#" class="text-blue-600 hover:underline">Terms</a>
      </div>
    </footer>

  </body>
</html>
