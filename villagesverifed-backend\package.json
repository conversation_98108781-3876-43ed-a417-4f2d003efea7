{"name": "villagesverifed-backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.10.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "pg": "^8.13.1"}, "devDependencies": {"nodemon": "^3.1.10", "prisma": "^6.10.1"}}