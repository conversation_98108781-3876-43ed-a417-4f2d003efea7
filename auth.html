<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login | VillagesVerified</title>
    <script src="https://cdn.tailwindcss.com"></script>
  </head>
  <body class="bg-blue-50 flex items-center justify-center min-h-screen">

    <div class="bg-white shadow-lg rounded-lg p-8 w-full max-w-md">
      <!-- Logo -->
      <div class="text-center mb-6">
        <h1 class="text-2xl font-bold text-blue-700">VillagesVerified</h1>
        <p class="text-sm text-gray-500">Login or create your account</p>
      </div>

      <!-- Tab Switch -->
      <div class="flex justify-center mb-6 space-x-4">
        <button id="loginTab" class="text-blue-700 font-semibold border-b-2 border-blue-700 pb-1">Login</button>
        <button id="registerTab" class="text-gray-500 hover:text-blue-700 pb-1">Register</button>
      </div>

      <!-- Login Form -->
      <form id="loginForm" class="space-y-4">
        <div>
          <label class="block text-sm font-medium">Email</label>
          <input type="email" class="w-full p-2 border rounded" placeholder="<EMAIL>" required />
        </div>
        <div>
          <label class="block text-sm font-medium">Password</label>
          <input type="password" class="w-full p-2 border rounded" placeholder="••••••••" required />
        </div>
        <button type="submit" class="w-full bg-blue-700 text-white py-2 rounded hover:bg-blue-800">Log In</button>
      </form>

      <!-- Register Form (Hidden Initially) -->
      <form id="registerForm" class="space-y-4 hidden">
        <div>
          <label class="block text-sm font-medium">Full Name</label>
          <input type="text" class="w-full p-2 border rounded" placeholder="John Doe" required />
        </div>
        <div>
          <label class="block text-sm font-medium">Email</label>
          <input type="email" class="w-full p-2 border rounded" placeholder="<EMAIL>" required />
        </div>
        <div>
          <label class="block text-sm font-medium">Create Password</label>
          <input type="password" class="w-full p-2 border rounded" placeholder="••••••••" required />
        </div>
        <button type="submit" class="w-full bg-blue-700 text-white py-2 rounded hover:bg-blue-800">Create Account</button>
      </form>

      <!-- Footer -->
      <div class="mt-6 text-center text-sm text-gray-400">
        &copy; 2025 VillagesVerified
      </div>
    </div>

    <!-- Tab Toggle Script -->
    <script>
      const loginTab = document.getElementById('loginTab');
      const registerTab = document.getElementById('registerTab');
      const loginForm = document.getElementById('loginForm');
      const registerForm = document.getElementById('registerForm');

      loginTab.addEventListener('click', () => {
        loginForm.classList.remove('hidden');
        registerForm.classList.add('hidden');
        loginTab.classList.add('text-blue-700', 'font-semibold', 'border-b-2', 'border-blue-700');
        registerTab.classList.remove('text-blue-700', 'border-b-2');
        registerTab.classList.add('text-gray-500');
      });

      registerTab.addEventListener('click', () => {
        loginForm.classList.add('hidden');
        registerForm.classList.remove('hidden');
        registerTab.classList.add('text-blue-700', 'font-semibold', 'border-b-2', 'border-blue-700');
        loginTab.classList.remove('text-blue-700', 'border-b-2');
        loginTab.classList.add('text-gray-500');
      });
    </script>

  </body>
</html>
