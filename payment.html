<!DOCTYPE html>
<html lang="en" >
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Upgrade Plan | VillagesVerified</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 text-gray-800">

  <!-- Header -->
  <header class="bg-white shadow">
    <div class="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
      <h1 class="text-xl font-bold text-blue-700">VillagesVerified</h1>
      <nav class="space-x-4">
        <a href="index.html" class="text-gray-600 hover:text-blue-600">Home</a>
        <a href="dashboard.html" class="text-gray-600 hover:text-blue-600">Dashboard</a>
        <a href="auth.html" class="text-gray-600 hover:text-blue-600">Logout</a>
      </nav>
    </div>
  </header>

  <!-- Upgrade Section -->
  <main class="max-w-3xl mx-auto p-6 bg-white rounded shadow mt-10">
    <h2 class="text-2xl font-bold text-blue-700 mb-6 text-center">Upgrade Your Plan</h2>

    <!-- Plans -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <div class="border rounded p-6 text-center">
        <h3 class="text-xl font-semibold mb-2">Basic</h3>
        <p class="text-gray-600 mb-4">$0 / month</p>
        <ul class="text-sm mb-4 space-y-1">
          <li>✔ Free Listing</li>
          <li>✔ Basic Contact Info</li>
          <li>✘ Reviews & Bookings</li>
        </ul>
        <button disabled class="bg-gray-300 text-gray-600 px-4 py-2 rounded cursor-not-allowed">Current Plan</button>
      </div>

      <div class="border rounded p-6 text-center border-blue-700 bg-blue-50">
        <h3 class="text-xl font-semibold mb-2 text-blue-700">Verified Pro</h3>
        <p class="text-blue-700 font-bold mb-4">$19 / month</p>
        <ul class="text-sm mb-4 space-y-1">
          <li>✔ Verified Badge</li>
          <li>✔ Customer Reviews</li>
          <li>✔ Direct Booking & Messaging</li>
          <li>✔ Priority Support</li>
        </ul>
        <button id="upgradeBtn" class="bg-blue-700 text-white px-4 py-2 rounded hover:bg-blue-800">Upgrade Now</button>
      </div>
    </div>

    <!-- Payment Form -->
    <form id="paymentForm" class="space-y-4 hidden">
      <div>
        <label class="block mb-1 font-medium">Cardholder Name</label>
        <input type="text" class="w-full border p-2 rounded" placeholder="John Doe" required />
      </div>
      <div>
        <label class="block mb-1 font-medium">Card Number</label>
        <input type="text" class="w-full border p-2 rounded" placeholder="1234 5678 9012 3456" required />
      </div>
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block mb-1 font-medium">Expiration Date</label>
          <input type="text" class="w-full border p-2 rounded" placeholder="MM/YY" required />
        </div>
        <div>
          <label class="block mb-1 font-medium">CVC</label>
          <input type="text" class="w-full border p-2 rounded" placeholder="123" required />
        </div>
      </div>
      <button type="submit" class="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700">Submit Payment</button>
    </form>
  </main>

  <!-- Footer -->
  <footer class="text-center text-gray-600 mt-12 mb-6">
    &copy; 2025 VillagesVerified
  </footer>

  <script>
    const upgradeBtn = document.getElementById('upgradeBtn');
    const paymentForm = document.getElementById('paymentForm');

    upgradeBtn.addEventListener('click', () => {
      paymentForm.classList.remove('hidden');
      upgradeBtn.disabled = true;
      upgradeBtn.textContent = 'Processing...';
    });

    // Placeholder for actual payment submission
    paymentForm.addEventListener('submit', (e) => {
      e.preventDefault();
      alert('Payment processed! (Integration coming soon)');
      paymentForm.reset();
      paymentForm.classList.add('hidden');
      upgradeBtn.disabled = false;
      upgradeBtn.textContent = 'Upgrade Now';
    });
  </script>
</body>
</html>
