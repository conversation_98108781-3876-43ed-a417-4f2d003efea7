const express = require('express');
const cors = require('cors');
require('dotenv').config();

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

const app = express();
app.use(cors());
app.use(express.json());

// Test root route
app.get('/', (req, res) => {
  res.send('VillagesVerified backend API is running');
});

// Sample API: Get all businesses
app.get('/api/businesses', async (req, res) => {
  try {
    const businesses = await prisma.business.findMany({
      include: { owner: { select: { id: true, fullName: true, email: true } } },
    });
    res.json(businesses);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Failed to fetch businesses' });
  }
});

const PORT = process.env.PORT || 4000;
app.listen(PORT, () => console.log(`Server started on http://localhost:${PORT}`));
